2025-08-04 13:12:58 - Sync from Foundation to .kiro completed
2025-08-04 13:12:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:12:59 - Sync from Foundation to .kiro completed
2025-08-04 13:12:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:00 - Sync from Foundation to .kiro completed
2025-08-04 13:13:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:01 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:02 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:03 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:04 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:05 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:06 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:07 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:08 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:09 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:10 - Sync from Foundation to .kiro completed
2025-08-04 13:13:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:11 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:12 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:13 - Sync from Foundation to .kiro completed
2025-08-04 13:13:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:14 - Sync from Foundation to .kiro completed
2025-08-04 13:13:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:15 - Sync from Foundation to .kiro completed
2025-08-04 13:13:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:16 - Sync from Foundation to .kiro completed
2025-08-04 13:13:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:17 - Sync from Foundation to .kiro completed
2025-08-04 13:13:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:18 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:19 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:20 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:21 - Sync from Foundation to .kiro completed
2025-08-04 13:13:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:22 - Sync from Foundation to .kiro completed
2025-08-04 13:13:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:23 - Sync from Foundation to .kiro completed
2025-08-04 13:13:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:24 - Sync from Foundation to .kiro completed
2025-08-04 13:13:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:25 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:26 - Sync from Foundation to .kiro completed
2025-08-04 13:13:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:27 - Sync from Foundation to .kiro completed
2025-08-04 13:13:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:28 - Sync from Foundation to .kiro completed
2025-08-04 13:13:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:29 - Sync from Foundation to .kiro completed
2025-08-04 13:13:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:30 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:31 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:32 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:33 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:34 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:35 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:36 - Sync from Foundation to .kiro completed
2025-08-04 13:13:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:37 - Sync from Foundation to .kiro completed
2025-08-04 13:13:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:38 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:39 - Sync from Foundation to .kiro completed
2025-08-04 13:13:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:40 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:41 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:42 - Sync from Foundation to .kiro completed
2025-08-04 13:13:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:43 - Sync from Foundation to .kiro completed
2025-08-04 13:13:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:44 - Sync from Foundation to .kiro completed
2025-08-04 13:13:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:45 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:46 - Sync from Foundation to .kiro completed
2025-08-04 13:13:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:47 - Sync from Foundation to .kiro completed
2025-08-04 13:13:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:48 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:49 - Sync from Foundation to .kiro completed
2025-08-04 13:13:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:50 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:51 - Sync from Foundation to .kiro completed
2025-08-04 13:13:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:52 - Sync from Foundation to .kiro completed
2025-08-04 13:13:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:52 - Sync from Foundation to .kiro completed
2025-08-04 13:13:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:52 - Sync from Foundation to .kiro completed
2025-08-04 13:13:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:52 - Sync from Foundation to .kiro completed
2025-08-04 13:13:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:52 - Sync from Foundation to .kiro completed
2025-08-04 13:13:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:52 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:53 - Sync from Foundation to .kiro completed
2025-08-04 13:13:53 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:54 - Sync from Foundation to .kiro completed
2025-08-04 13:13:54 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:55 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:55 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:55 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:55 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:55 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:55 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:55 - Sync from Foundation to .kiro completed
2025-08-04 13:13:56 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:56 - Sync from Foundation to .kiro completed
2025-08-04 13:13:56 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:56 - Sync from Foundation to .kiro completed
2025-08-04 13:13:56 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:56 - Sync from Foundation to .kiro completed
2025-08-04 13:13:56 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:56 - Sync from Foundation to .kiro completed
2025-08-04 13:13:56 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:56 - Sync from Foundation to .kiro completed
2025-08-04 13:13:56 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:56 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:57 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:57 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:58 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:58 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:13:59 - Sync from Foundation to .kiro completed
2025-08-04 13:13:59 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:00 - Sync from Foundation to .kiro completed
2025-08-04 13:14:00 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:01 - Sync from Foundation to .kiro completed
2025-08-04 13:14:01 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:02 - Sync from Foundation to .kiro completed
2025-08-04 13:14:02 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:03 - Sync from Foundation to .kiro completed
2025-08-04 13:14:03 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:04 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:04 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:05 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:05 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:06 - Sync from Foundation to .kiro completed
2025-08-04 13:14:06 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:07 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:07 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:08 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:08 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:09 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:09 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:10 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:10 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:11 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:11 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:12 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:12 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:13 - Sync from Foundation to .kiro completed
2025-08-04 13:14:13 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:14 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:14 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:15 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:15 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:16 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:16 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:17 - Sync from Foundation to .kiro completed
2025-08-04 13:14:17 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:18 - Sync from Foundation to .kiro completed
2025-08-04 13:14:18 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:19 - Sync from Foundation to .kiro completed
2025-08-04 13:14:19 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:20 - Sync from Foundation to .kiro completed
2025-08-04 13:14:20 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:21 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:21 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:22 - Sync from Foundation to .kiro completed
2025-08-04 13:14:22 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:23 - Sync from Foundation to .kiro completed
2025-08-04 13:14:23 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:24 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:24 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:25 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:25 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:26 - Sync from Foundation to .kiro completed
2025-08-04 13:14:26 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:27 - Sync from Foundation to .kiro completed
2025-08-04 13:14:27 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:28 - Sync from Foundation to .kiro completed
2025-08-04 13:14:28 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:29 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:29 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:30 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:30 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:31 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:31 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:32 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:32 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:33 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:33 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  705.33 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:34 - Sync from Foundation to .kiro completed
2025-08-04 13:14:34 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:35 - Sync from Foundation to .kiro completed
2025-08-04 13:14:35 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:36 - Sync from Foundation to .kiro completed
2025-08-04 13:14:36 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:37 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:37 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:38 - Sync from Foundation to .kiro completed
2025-08-04 13:14:38 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:39 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:39 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:40 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:40 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:41 - Sync from Foundation to .kiro completed
2025-08-04 13:14:41 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:42 - Sync from Foundation to .kiro completed
2025-08-04 13:14:42 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:43 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:43 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:44 - Sync from Foundation to .kiro completed
2025-08-04 13:14:44 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:45 - Sync from Foundation to .kiro completed
2025-08-04 13:14:45 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:46 - Sync from Foundation to .kiro completed
2025-08-04 13:14:46 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:47 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:47 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:48 - Sync from Foundation to .kiro completed
2025-08-04 13:14:48 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:49 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:49 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:50 - Sync from Foundation to .kiro completed
2025-08-04 13:14:50 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:51 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:51 - Sync from Foundation to .kiro completed
2025-08-04 13:14:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:52 - Sync from Foundation to .kiro completed
2025-08-04 13:14:52 - Starting sync from Foundation to .kiro
sending incremental file list

sent 1,040 bytes  received 18 bytes  2,116.00 bytes/sec
total size is 169,825  speedup is 160.52
2025-08-04 13:14:52 - Sync from Foundation to .kiro completed
